import { IAdventure } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseUpdateAdventure(){
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IAdventure>, Error, IAdventure>({
        mutationFn: (data: IAdventure) =>
            fetch(`https://api.trailandtreknepal.com/home-adventure/${data.id}`, {
                method: "PATCH",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['adventure'] });
            toast.success("Adventure Updated Sucessfully");
        },
        onError: () => {
            toast.error("Error Updating Adventure");
        }
    })
}